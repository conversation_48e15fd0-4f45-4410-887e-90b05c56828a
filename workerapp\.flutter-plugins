# This is a generated file; do not edit or check into version control.
desktop_window=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\desktop_window-0.4.2\\
file_picker=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_picker-10.2.0\\
file_selector_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_linux-0.9.3+2\\
file_selector_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_macos-0.9.4+3\\
file_selector_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file_selector_windows-0.9.3+4\\
firebase_core=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core-3.15.1\\
firebase_core_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_core_web-2.24.1\\
firebase_messaging=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging-15.2.9\\
firebase_messaging_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_messaging_web-3.10.9\\
flutter_downloader=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_downloader-1.12.0\\
flutter_image_compress=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress-2.4.0\\
flutter_image_compress_common=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_common-1.0.6\\
flutter_image_compress_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_macos-1.0.3\\
flutter_image_compress_ohos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_ohos-0.0.3\\
flutter_image_compress_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_web-0.1.5\\
flutter_localization=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_localization-0.3.3\\
flutter_plugin_android_lifecycle=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.28\\
image_cropper=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_cropper-9.1.0\\
image_cropper_for_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_cropper_for_web-6.1.0\\
image_picker=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker-1.1.2\\
image_picker_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_android-0.8.12+23\\
image_picker_for_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_for_web-3.0.6\\
image_picker_ios=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_ios-0.8.12+2\\
image_picker_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_linux-0.2.1+2\\
image_picker_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_macos-0.2.1+2\\
image_picker_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\image_picker_windows-0.2.1+1\\
path_provider=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\
path_provider_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.17\\
path_provider_foundation=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\
path_provider_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\
path_provider_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\
permission_handler=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler-12.0.1\\
permission_handler_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_android-13.0.1\\
permission_handler_apple=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_apple-9.4.7\\
permission_handler_html=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_html-0.1.3+5\\
permission_handler_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\permission_handler_windows-0.2.1\\
printing=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\printing-5.14.2\\
share_plus=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\share_plus-11.0.0\\
shared_preferences=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\
shared_preferences_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\
shared_preferences_foundation=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\
shared_preferences_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\
shared_preferences_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\
shared_preferences_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\
url_launcher=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\
url_launcher_android=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\
url_launcher_ios=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\
url_launcher_linux=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\
url_launcher_macos=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\
url_launcher_web=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\
url_launcher_windows=C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\
