{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\worklink-app\\workerapp\\android\\app\\.cxx\\RelWithDebInfo\\3s4i2835\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\worklink-app\\workerapp\\android\\app\\.cxx\\RelWithDebInfo\\3s4i2835\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}