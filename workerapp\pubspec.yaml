name: work_link
description: WorkLink Mobile app

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.4+22

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.0.0"
dependencies:
  flutter:
    sdk: flutter
  animate_do: ^4.2.0
  cupertino_icons: ^1.0.2
  dio: ^5.1.1
  flash: ^3.1.1
  flutter_form_builder: ^10.0.1
  form_builder_validators: ^11.0.0
  flutter_riverpod: ^2.0.0
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0
  google_fonts: ^6.2.1
  intl: any
  line_icons: ^2.0.1
  logger: ^2.6.0
  modal_bottom_sheet: ^3.0.0
  ndialog: ^4.1.0
  page_transition: ^2.0.2
  relative_scale: ^2.0.0
  shared_preferences: ^2.0.6
  styled_widget: ^0.4.1
  swipeable_tile: ^2.0.1
  duration: ^4.0.3
  badges: ^3.1.2
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  # date_range_form_field: ^1.0.2  # Temporarily disabled due to intl version conflict
  calendar_date_picker2: ^2.0.1
  url_launcher: ^6.2.2
  # smart_calendar: ^0.0.2  # Temporarily disabled - doesn't support null safety
  # month_year_picker: ^0.3.0+1  # Temporarily disabled due to intl version conflict
  path_provider: ^2.0.12
  permission_handler: ^12.0.1
  firebase_messaging: ^15.2.9
  # open_file: ^3.2.1
  percent_indicator: ^4.2.2
  flutter_downloader: ^1.10.1+2
  syncfusion_flutter_datepicker: ^30.1.39
  table_calendar: ^3.0.0
  colorize_text_avatar: ^1.0.2
  syncfusion_localizations: ^30.1.39
  desktop_window: ^0.4.0
  flutter_image_compress: ^2.1.0

  # open_file_plus: ^3.4.1  # Temporarily disabled due to v1 embedding issues
  share_plus: ^11.0.0
  printing: ^5.11.1
  pdf: ^3.10.4
  web_socket_channel: ^3.0.3
  flutter_localization: ^0.3.3
  freezed: ^3.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.7
  change_app_package_name: ^1.0.0

  json_serializable: ^6.7.1
  flutter_launcher_icons: ^0.14.4
  dio: ^5.1.1
  file_picker: ^10.2.0

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/icon.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  generate: true
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/drawer/
    - assets/fonts/Roboto/
