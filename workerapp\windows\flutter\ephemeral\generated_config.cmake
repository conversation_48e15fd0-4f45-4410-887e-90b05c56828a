# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\worklink-app\\workerapp" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.4+22" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 4 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 22 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\worklink-app\\workerapp"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\worklink-app\\workerapp\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\worklink-app\\workerapp"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\worklink-app\\workerapp\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\worklink-app\\workerapp\\.dart_tool\\package_config.json"
)
